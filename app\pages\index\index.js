import responsive from '../../utils/responsive.js';

let that = null,
	app = getApp(),
	config = app.globalData.config,
	cache = config.storage,
	helper = app.globalData.helper;

export default {
	// 混入响应式功能
	...responsive.pageMixin,

	data() {
		return {
			images: [],
			appIsAudit: false,
			showWxGroup: false,
			showWxGroupText: '',
			appPlatform: app.globalData.appPlatform,
			showIndexShareName: '',
			articleList: [],
			currentCity: {},
			currentExam: {},
			currentProfession: {},
			menuList: [],
			examTime: {},
			isLoading: true,
			// 响应式相关数据（由混入提供）
			// deviceInfo: {},
			// isPC: false,
			// maxRpxHeight: 0,
			// platformClass: '',
			// screenClass: '',

			// 原有PC端适配数据（保留兼容性）
			isPCWeixin: false,
			screenType: 'mobile',

			// 动态样式
			dynamicStyles: {
				scrollViewHeight: '400rpx',
				containerPadding: '20rpx'
			}
		};
	},
	computed: {
		pageClasses() {
			return 'index-page' + (this.isPC ? ' pc-mode' : '');
		},
		searchActionClass() {
			return this.isPC ? 'search-action-pc' : 'search-action-mobile';
		},
		// 动态计算滚动区域高度
		scrollViewHeight() {
			if (!this.maxRpxHeight) return '400rpx';

			// 减去顶部搜索栏、轮播图、倒计时等固定高度
			const fixedHeight = this.isPC ? 200 : 400; // px 或 rpx

			if (this.isPC) {
				// PC端使用px
				const availableHeight = this.rpxToPx(this.maxRpxHeight) - fixedHeight;
				return `${Math.max(300, availableHeight)}px`;
			} else {
				// 移动端使用rpx
				const availableHeight = this.maxRpxHeight - fixedHeight;
				return `${Math.max(600, availableHeight)}rpx`;
			}
		}
	},
	onLoad() {
		app.globalData.showShareMenu();
		that = this;
		// 初始化响应式功能（替代原有的detectPC）
		this.initResponsive();
		// 更新动态样式
		this.updateDynamicStyles();
	},
	onResize() {
		// 窗口大小改变时重新初始化
		this.initResponsive();
		this.updateDynamicStyles();
	},
	onShow() {
		that.getHomeData();
		that.initIndexExamData();
	},
	onShareAppMessage() {
		return app.globalData.getShareConfig();
	},
	methods: {
		/**
		 * 更新动态样式
		 */
		updateDynamicStyles() {
			// 使用响应式工具计算样式
			const containerPadding = this.getAdaptiveSize({
				mobile: 20,
				pc: 16
			});

			const cardMargin = this.getAdaptiveSize({
				mobile: 15,
				pc: 12
			});

			this.setData({
				dynamicStyles: {
					scrollViewHeight: this.scrollViewHeight,
					containerPadding,
					cardMargin
				}
			});
		},

		/**
		 * 获取响应式尺寸（便捷方法）
		 */
		getResponsiveSize(mobileSize, pcSize) {
			return this.isPC ? `${pcSize}px` : `${mobileSize}rpx`;
		},

		/**
		 * 计算可用高度（扣除固定元素）
		 */
		getAvailableHeight(excludeHeight = 0) {
			const totalHeight = this.isPC ?
				this.rpxToPx(this.maxRpxHeight) :
				this.maxRpxHeight;

			const availableHeight = totalHeight - excludeHeight;
			return this.isPC ? `${availableHeight}px` : `${availableHeight}rpx`;
		},

		/**
		 * 简单的PC端检测（保留兼容性）
		 */
		detectPC() {
			try {
				// 获取系统信息
				const systemInfo = uni.getSystemInfoSync();
				const windowWidth = systemInfo.windowWidth;

				// 更新判断标准：600px以上认为是PC端
				that.isPC = windowWidth >= 600;
				that.screenType = windowWidth >= 600 ? 'desktop' : 'mobile';

				console.log('PC端检测结果:', {
					windowWidth: windowWidth,
					isPC: that.isPC,
					screenType: that.screenType
				});
			} catch (error) {
				console.error('PC端检测失败:', error);
				that.isPC = false;
				that.screenType = 'mobile';
			}
		},

		/**
		 * 通用导航方法
		 * @param {String} url 完整的导航地址
		 */
		goTo(url) {
			uni.navigateTo({url});
		},
		
		checkAppIsAudit() {
			that.appIsAudit = app.globalData.checkAppIsAudit();
		},
		initIndexExamData() {
			that.currentCity = cache.getCurrentCityData();
			that.currentExam = cache.getCurrentExamData();
			that.currentProfession = cache.getCurrentProfessionData();
			that.changeExamInfo();
		},
		async changeExamInfo() {
			let exam_id = helper.variableDefalut(that.currentExam.id, 0),
				region_id = helper.variableDefalut(that.currentCity.id, 0),
				profession_id = helper.variableDefalut(that.currentProfession.id, 0);
			if (region_id == 0) {
				that.goTo('city/city?first_visit=1');
				return;
			}
			if (profession_id && app.globalData.isLogin) {
				let user = cache.getUserInfoData(),
					user_profession_id = helper
					.variableDefalut(user.profession_id, 0);
				if (user_profession_id != profession_id) {
					await app.globalData.service.userUpdate({
						exam_id: exam_id,
						region_id: region_id,
						profession_id: profession_id
					});
					user.profession_id = profession_id;
					cache.setUserInfoData(user);
				}
			}
		},
		
		/**
		 * 获取首页数据
		 */
		getHomeData() {
			that.isLoading = true;
			// 优先加载缓存
			let time = app.globalData.getTimestamp();
			let cacheHomeData = wx.getStorageSync(cache.homeKey)
			console.log(cacheHomeData, time);
			if (cacheHomeData && cacheHomeData.expire >= time) {
				that.images = cacheHomeData.swiper;
				that.menuList = cacheHomeData.menuList
				that.articleList = cacheHomeData.articleList;
				that.showWxGroup = cacheHomeData.appConfig.showWxGroup
				that.showWxGroupText = cacheHomeData.appConfig.showWxGroupText
				that.showIndexShareName = cacheHomeData.appConfig.showIndexShareName
				// 从缓存中获取考试时间数据
				if (cacheHomeData.examTime) {
					that.examTime = cacheHomeData.examTime;
				}
				that.isLoading = false;
				return;
			}

			// 加载接口数据
			let cacheTime = 0;
			app.globalData.server
				.getRequest('home', {})
				.then((res) => {
					that.images = res.data.swiper;
					that.menuList = res.data.menuList;
					that.articleList = res.data.articleList;
					that.showWxGroup = res.data.appConfig.showWxGroup;
					that.showWxGroupText = res.data.appConfig.showWxGroupText;
					that.showIndexShareName = res.data.appConfig.showIndexShareName;
					
					// 获取考试时间数据
					if (res.data.examTime) {
						that.examTime = res.data.examTime;
					}
					
					res.data.expire = time + cacheTime;
					cache.setHomeData(res.data);
					that.checkAppIsAudit();
					that.isLoading = false;
				})
				.catch((e) => {
					console.log(e)
					app.showToast('获取首页数据失败');
					that.isLoading = false;
				});
		}
	}
};