/* 移动端默认样式 (rpx单位) */
.menu-image {
	width: 56rpx;
	height: 56rpx;
	margin-bottom: 10rpx;
}
/* 搜索栏动作按钮样式 */
.search-action-mobile {
	font-size: 27rpx;
	max-width: 33.3333%;
}
.search-action-pc {
	font-size: 14px;
	padding: 8px 12px;
	border-radius: 6px;
	background-color: rgba(255, 255, 255, 0.9);
	margin: 0 5px;
}
/* 考试倒计时样式 */
.exam-countdown {
	padding: 0;
	border-radius: 12rpx;
	margin: 0 20rpx;
	overflow: hidden;
}
.countdown-content {
	background: linear-gradient(135deg, #f0f8ff, #e6f2ff);
	border-radius: 12rpx;
	box-shadow: 0 4rpx 12rpx rgba(0, 100, 255, 0.15);
	padding: 0;
	position: relative;
	overflow: hidden;
}
.countdown-header {
	background-color: #0081ff;
	color: #ffffff;
	padding: 16rpx 24rpx;
	display: flex;
	align-items: center;
}
.countdown-title {
	font-size: 28rpx;
	font-weight: 500;
	margin-left: 10rpx;
}
.countdown-info {
	padding: 20rpx 24rpx;
	display: flex;
	justify-content: space-between;
	align-items: center;
}
.countdown-date {
	font-size: 26rpx;
	color: #555;
}
.countdown-timer {
	display: flex;
	align-items: center;
}
.countdown-days {
	font-size: 40rpx;
	font-weight: bold;
	color: #0081ff;
	background-color: rgba(0, 129, 255, 0.1);
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	margin-right: 8rpx;
}
.countdown-unit {
	font-size: 28rpx;
	color: #555;
}
/* 新的宫格列表样式 */
.modern-grid {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	padding: 20rpx 10rpx;
}
.modern-grid-item {
	width: 33.33%;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 20rpx 0;
	transition: all 0.3s ease;
}
.modern-grid-item:active {
	-webkit-transform: scale(0.95);
	        transform: scale(0.95);
	opacity: 0.8;
}
.modern-grid-icon {
	width: 60rpx;
	height: 60rpx;
	margin-bottom: 10rpx;
	border-radius: 12rpx;
}
.modern-grid-text {
	font-size: 26rpx;
	color: #333;
	font-weight: 500;
}
/* PC端样式适配 (px单位) - 针对微信小程序PC端 */
/* 当屏幕宽度大于等于1024px时应用PC端样式 */
/* PC端页面容器 */
.index-page.pc-mode {
	background-color: #f8f9fa;
	min-height: 100vh;
}
.index-page.pc-mode .pc-content {
	width: 100%;
	padding: 20px;
	background-color: transparent;
}
/* 响应式断点样式 - 统一设计系统 */
@media (min-width: 1024px) {
	/* 全局字体大小基准 */
:root {
		--pc-font-xs: 12px;      /* 辅助文字 */
		--pc-font-sm: 14px;      /* 正文 */
		--pc-font-md: 16px;      /* 标题 */
		--pc-font-lg: 18px;      /* 大标题 */
		--pc-font-xl: 20px;      /* 主标题 */

		--pc-spacing-xs: 8px;    /* 最小间距 */
		--pc-spacing-sm: 12px;   /* 小间距 */
		--pc-spacing-md: 16px;   /* 中等间距 */
		--pc-spacing-lg: 20px;   /* 大间距 */
		--pc-spacing-xl: 24px;   /* 最大间距 */

		--pc-radius-sm: 6px;     /* 小圆角 */
		--pc-radius-md: 8px;     /* 中等圆角 */
		--pc-radius-lg: 12px;    /* 大圆角 */

		--pc-shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
		--pc-shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
		--pc-shadow-lg: 0 6px 16px rgba(0, 0, 0, 0.10);
}

	/* 搜索栏PC端适配 - 统一高度和字体 */
.cu-bar.search {
		padding: 0 var(--pc-spacing-lg);
		margin-bottom: var(--pc-spacing-md);
		height: 56px;
		display: flex;
		align-items: center;
}
.cu-bar.search .action {
		font-size: var(--pc-font-sm) !important;
		padding: var(--pc-spacing-xs) var(--pc-spacing-sm);
		border-radius: var(--pc-radius-sm);
		background-color: rgba(255, 255, 255, 0.95);
		margin: 0 var(--pc-spacing-xs);
		height: 40px;
		display: flex;
		align-items: center;
		box-shadow: var(--pc-shadow-sm);
		transition: all 0.2s ease;
}
.cu-bar.search .action:hover {
		box-shadow: var(--pc-shadow-md);
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
}

	/* 轮播图PC端适配 - 统一高度比例 */
.swiper {
		margin: 0 var(--pc-spacing-lg) var(--pc-spacing-lg);
		border-radius: var(--pc-radius-lg);
		overflow: hidden;
		box-shadow: var(--pc-shadow-md);
}
.swiper-image {
		height: 240px;
		object-fit: cover;
}

	/* 考试倒计时PC端适配 - 统一内容高度 */
.exam-countdown {
		margin: 0 var(--pc-spacing-lg) var(--pc-spacing-lg);
		border-radius: var(--pc-radius-md);
}
.countdown-content {
		border-radius: var(--pc-radius-md);
		box-shadow: var(--pc-shadow-md);
		min-height: 120px;
}
.countdown-header {
		padding: var(--pc-spacing-md) var(--pc-spacing-lg);
		height: 56px;
		display: flex;
		align-items: center;
}
.countdown-title {
		font-size: var(--pc-font-md);
		margin-left: var(--pc-spacing-xs);
		font-weight: 600;
}
.countdown-info {
		padding: var(--pc-spacing-md) var(--pc-spacing-lg);
		display: flex;
		align-items: center;
		justify-content: space-between;
}
.countdown-date {
		font-size: var(--pc-font-sm);
		color: #666;
}
.countdown-days {
		font-size: var(--pc-font-lg);
		padding: var(--pc-spacing-xs) var(--pc-spacing-sm);
		border-radius: var(--pc-radius-sm);
		margin-right: var(--pc-spacing-xs);
		font-weight: 600;
}
.countdown-unit {
		font-size: var(--pc-font-md);
		font-weight: 500;
}

	/* 学习中心标题栏PC端适配 - 统一标题样式 */
.cu-bar {
		padding: 0 var(--pc-spacing-lg);
		margin-bottom: var(--pc-spacing-sm);
		height: 64px;
		display: flex;
		align-items: center;
		justify-content: space-between;
}
.cu-bar .action.sub-title {
		display: flex;
		align-items: baseline;
		gap: var(--pc-spacing-xs);
}
.cu-bar .text-xl {
		font-size: var(--pc-font-xl) !important;
		font-weight: 600 !important;
}
.cu-bar .text-ABC {
		font-size: var(--pc-font-xs) !important;
		opacity: 0.7;
}
.cu-bar .cu-btn {
		font-size: var(--pc-font-sm) !important;
		padding: var(--pc-spacing-xs) var(--pc-spacing-md) !important;
		height: 40px !important;
		border-radius: var(--pc-radius-sm) !important;
		box-shadow: var(--pc-shadow-sm) !important;
		transition: all 0.2s ease !important;
}
.cu-bar .cu-btn:hover {
		box-shadow: var(--pc-shadow-md) !important;
		-webkit-transform: translateY(-1px) !important;
		        transform: translateY(-1px) !important;
}

	/* 宫格列表PC端适配 - 统一网格系统 */
.modern-grid {
		margin: 0 var(--pc-spacing-lg) var(--pc-spacing-xl);
		padding: var(--pc-spacing-lg);
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
		gap: var(--pc-spacing-md);
		background-color: #ffffff;
		border-radius: var(--pc-radius-lg);
		box-shadow: var(--pc-shadow-md);
}
.modern-grid-item {
		width: auto;
		padding: var(--pc-spacing-lg) var(--pc-spacing-sm);
		border-radius: var(--pc-radius-md);
		background-color: rgba(248, 249, 250, 0.5);
		transition: all 0.3s ease;
		cursor: pointer;
		min-height: 100px;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
}
.modern-grid-item:hover {
		-webkit-transform: translateY(-3px);
		        transform: translateY(-3px);
		box-shadow: var(--pc-shadow-lg);
		background-color: rgba(255, 255, 255, 0.9);
}
.modern-grid-item:active {
		-webkit-transform: translateY(-1px);
		        transform: translateY(-1px);
}
.modern-grid-icon {
		width: 44px;
		height: 44px;
		margin-bottom: var(--pc-spacing-xs);
		border-radius: var(--pc-radius-md);
}
.modern-grid-text {
		font-size: var(--pc-font-sm);
		font-weight: 500;
		text-align: center;
		color: #333;
}

	/* 资讯卡片PC端适配 - 统一卡片样式 */
.cu-card.article {
		margin: 0 var(--pc-spacing-lg);
		padding: 0;
}
.cu-card.article .cu-item {
		margin-bottom: var(--pc-spacing-md);
		border-radius: var(--pc-radius-lg);
		transition: all 0.3s ease;
		cursor: pointer;
		background-color: #ffffff;
		box-shadow: var(--pc-shadow-sm);
		overflow: hidden;
}
.cu-card.article .cu-item:hover {
		-webkit-transform: translateY(-2px);
		        transform: translateY(-2px);
		box-shadow: var(--pc-shadow-lg);
}
.cu-card.article .content {
		padding: var(--pc-spacing-lg);
		display: flex;
		align-items: center;
		min-height: 100px;
}
.cu-card.article .content image {
		width: 72px;
		height: 72px;
		border-radius: var(--pc-radius-md);
		flex-shrink: 0;
}
.cu-card.article .desc {
		margin-left: var(--pc-spacing-md);
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: center;
}
.cu-card.article .text-df {
		font-size: var(--pc-font-md);
		line-height: 1.5;
		margin-bottom: var(--pc-spacing-xs);
		color: #333;
		font-weight: 500;
}
.cu-card.article .text-sm {
		font-size: var(--pc-font-xs);
		color: #666;
		line-height: 1.4;
}
.cu-card.article .flex.justify-between {
		margin-top: var(--pc-spacing-xs);
}
}

