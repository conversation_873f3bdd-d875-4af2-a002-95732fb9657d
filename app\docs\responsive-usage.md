# 响应式工具使用指南

## 概述

这个响应式工具解决了uni-app开发中px与rpx转换的问题，并提供了统一的响应式布局方案。

### 主要特性

- **自动单位转换**：PC端使用px，移动端使用rpx
- **设备检测**：600px以上自动切换到PC端样式
- **高度计算**：自动计算设备可用最大rpx高度
- **样式混入**：提供页面混入对象，快速集成响应式功能

## 基本使用

### 1. 导入工具

```javascript
import responsive from '../../utils/responsive.js';
```

### 2. 页面混入

```javascript
export default {
  // 混入响应式功能
  ...responsive.pageMixin,
  
  data() {
    return {
      // 你的数据
    };
  },
  
  onLoad() {
    // 混入会自动调用 this.initResponsive()
    // 你可以在这里添加其他初始化逻辑
  }
}
```

### 3. 模板中使用

```html
<!-- 使用平台类名 -->
<view :class="platformClass">
  <!-- 使用动态高度 -->
  <scroll-view scroll-y :style="{height: scrollViewHeight}">
    <!-- 内容 -->
  </scroll-view>
</view>

<!-- 使用设备信息 -->
<view v-if="isPC">PC端内容</view>
<view v-else>移动端内容</view>
```

## 核心API

### 设备检测

```javascript
// 判断设备类型
this.isPC          // 是否为PC端（>=600px）
this.isMobile()    // 是否为移动端（<600px）
this.isTablet()    // 是否为平板（768px-1024px）

// 获取设备信息
this.deviceInfo    // 完整设备信息对象
this.maxRpxHeight  // 设备可用最大rpx高度
```

### 单位转换

```javascript
// px转rpx
const rpxValue = this.pxToRpx(100); // 100px转为rpx

// rpx转px
const pxValue = this.rpxToPx(200); // 200rpx转为px

// 智能转换（推荐）
const size = this.getAdaptiveSize(20); // PC端20px，移动端20rpx
const size2 = this.getAdaptiveSize({mobile: 30, pc: 20}); // 指定不同值
```

### 样式创建

```javascript
// 创建响应式样式对象
const styles = this.createResponsiveStyle({
  height: {mobile: 400, pc: 300},
  padding: 20, // 自动转换单位
  'border-radius': {mobile: 8, pc: 6}
});

// 转换为样式字符串
const styleString = Object.entries(styles)
  .map(([key, value]) => `${key}: ${value}`)
  .join('; ');
```

## 实际应用示例

### 1. 动态滚动区域高度

```javascript
computed: {
  scrollViewHeight() {
    // 减去固定元素高度
    const fixedHeight = this.isPC ? 200 : 400;
    
    if (this.isPC) {
      const availableHeight = this.rpxToPx(this.maxRpxHeight) - fixedHeight;
      return `${Math.max(300, availableHeight)}px`;
    } else {
      const availableHeight = this.maxRpxHeight - fixedHeight;
      return `${Math.max(600, availableHeight)}rpx`;
    }
  }
}
```

### 2. 响应式卡片样式

```javascript
methods: {
  getCardStyle() {
    return this.createResponsiveStyle({
      height: {mobile: 120, pc: 80},
      padding: {mobile: 20, pc: 16},
      'margin-bottom': {mobile: 15, pc: 12},
      'border-radius': 8 // 自动转换单位
    });
  }
}
```

### 3. 条件渲染

```html
<template>
  <!-- PC端布局 -->
  <view v-if="isPC" class="pc-layout">
    <view class="sidebar">侧边栏</view>
    <view class="main-content">主内容</view>
  </view>
  
  <!-- 移动端布局 -->
  <view v-else class="mobile-layout">
    <view class="mobile-content">移动端内容</view>
  </view>
</template>
```

### 4. 动态样式绑定

```html
<template>
  <view 
    :style="{
      height: getResponsiveSize(400, 300),
      padding: getResponsiveSize(20, 16)
    }"
  >
    内容区域
  </view>
</template>

<script>
methods: {
  getResponsiveSize(mobileSize, pcSize) {
    return this.isPC ? `${pcSize}px` : `${mobileSize}rpx`;
  }
}
</script>
```

## 最佳实践

### 1. 统一设计系统

```javascript
// 定义设计规范
const designSystem = {
  spacing: {
    xs: {mobile: 8, pc: 4},
    sm: {mobile: 16, pc: 8},
    md: {mobile: 24, pc: 16},
    lg: {mobile: 32, pc: 24}
  },
  fontSize: {
    sm: {mobile: 24, pc: 12},
    md: {mobile: 28, pc: 14},
    lg: {mobile: 32, pc: 16}
  }
};

// 使用
const spacing = this.getAdaptiveSize(designSystem.spacing.md);
```

### 2. 组件封装

```javascript
// 响应式组件
export default {
  props: {
    height: {
      type: [Number, Object],
      default: 100
    }
  },
  computed: {
    adaptiveHeight() {
      return this.getAdaptiveSize(this.height);
    }
  }
}
```

### 3. 样式预设

```javascript
// 常用样式预设
const presets = {
  card: {
    'border-radius': 8,
    padding: {mobile: 20, pc: 16},
    'box-shadow': 'pc-only'
  },
  button: {
    height: {mobile: 80, pc: 40},
    'border-radius': 4,
    padding: {mobile: 20, pc: 12}
  }
};
```

## 注意事项

1. **混入顺序**：确保响应式混入在其他混入之前
2. **生命周期**：onResize事件会自动更新响应式数据
3. **性能优化**：避免在computed中频繁调用转换函数
4. **兼容性**：保持与原有PC检测逻辑的兼容性

## 迁移指南

### 从原有方案迁移

```javascript
// 原有方式
if (this.isPC) {
  height = '300px';
} else {
  height = '600rpx';
}

// 新方式
height = this.getResponsiveSize(600, 300);
```

这个工具让响应式开发变得更加简单和统一，避免了手动处理px/rpx转换的繁琐工作。
