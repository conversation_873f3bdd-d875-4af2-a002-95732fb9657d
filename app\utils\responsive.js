// 响应式工具函数 - 增强版
const responsive = {
  // 获取当前设备信息
  getDeviceInfo() {
    const systemInfo = uni.getSystemInfoSync();
    return {
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      platform: systemInfo.platform,
      pixelRatio: systemInfo.pixelRatio || 1
    };
  },

  // 判断是否为PC端 (600px以上)
  isPC() {
    const deviceInfo = this.getDeviceInfo();
    return deviceInfo.windowWidth >= 600;
  },

  // 判断是否为平板
  isTablet() {
    const deviceInfo = this.getDeviceInfo();
    return deviceInfo.windowWidth >= 768 && deviceInfo.windowWidth < 1024;
  },

  // 判断是否为手机
  isMobile() {
    const deviceInfo = this.getDeviceInfo();
    return deviceInfo.windowWidth < 600;
  },

  // 计算设备可用最大rpx高度
  getMaxRpxHeight() {
    const deviceInfo = this.getDeviceInfo();
    return deviceInfo.windowHeight / (deviceInfo.windowWidth / 750);
  },

  // px转rpx
  pxToRpx(px) {
    const deviceInfo = this.getDeviceInfo();
    return (px * 750) / deviceInfo.windowWidth;
  },

  // rpx转px
  rpxToPx(rpx) {
    const deviceInfo = this.getDeviceInfo();
    return (rpx * deviceInfo.windowWidth) / 750;
  },

  // 智能单位转换 - 根据设备类型自动选择合适的值
  getAdaptiveSize(config) {
    const isPC = this.isPC();
    
    if (typeof config === 'number') {
      // 如果传入的是数字，PC端使用px，移动端使用rpx
      return isPC ? `${config}px` : `${config}rpx`;
    }
    
    if (typeof config === 'object') {
      // 如果传入的是配置对象
      if (isPC && config.pc !== undefined) {
        return typeof config.pc === 'number' ? `${config.pc}px` : config.pc;
      } else if (config.mobile !== undefined) {
        return typeof config.mobile === 'number' ? `${config.mobile}rpx` : config.mobile;
      }
    }
    
    return config;
  },

  // 获取响应式值
  getResponsiveValue(mobileValue, tabletValue, pcValue) {
    if (this.isPC()) {
      return pcValue || tabletValue || mobileValue;
    } else if (this.isTablet()) {
      return tabletValue || mobileValue;
    } else {
      return mobileValue;
    }
  },

  // 获取平台类名
  getPlatformClass() {
    if (this.isPC()) {
      return 'pc-mode';
    } else if (this.isTablet()) {
      return 'tablet-mode';
    } else {
      return 'mobile-mode';
    }
  },

  // 获取屏幕类名
  getScreenClass() {
    const deviceInfo = this.getDeviceInfo();
    if (deviceInfo.windowWidth >= 1200) {
      return 'screen-xl';
    } else if (deviceInfo.windowWidth >= 1024) {
      return 'screen-lg';
    } else if (deviceInfo.windowWidth >= 768) {
      return 'screen-md';
    } else if (deviceInfo.windowWidth >= 600) {
      return 'screen-sm';
    } else {
      return 'screen-xs';
    }
  },

  // 创建响应式样式对象
  createResponsiveStyle(styleConfig) {
    const result = {};
    const isPC = this.isPC();
    
    for (const [property, value] of Object.entries(styleConfig)) {
      if (typeof value === 'object' && value !== null) {
        // 响应式配置
        if (isPC && value.pc !== undefined) {
          result[property] = this.getAdaptiveSize(value.pc);
        } else if (value.mobile !== undefined) {
          result[property] = this.getAdaptiveSize(value.mobile);
        }
      } else {
        // 普通值
        result[property] = this.getAdaptiveSize(value);
      }
    }
    
    return result;
  },

  // 页面混入对象 - 可以直接在页面中使用
  pageMixin: {
    data() {
      return {
        // 设备信息
        deviceInfo: {},
        // 是否为PC端
        isPC: false,
        // 最大rpx高度
        maxRpxHeight: 0,
        // 平台类名
        platformClass: '',
        // 屏幕类名
        screenClass: ''
      };
    },
    
    onLoad() {
      this.initResponsive();
    },
    
    onResize() {
      this.initResponsive();
    },
    
    methods: {
      // 初始化响应式数据
      initResponsive() {
        const deviceInfo = responsive.getDeviceInfo();
        this.setData({
          deviceInfo,
          isPC: responsive.isPC(),
          maxRpxHeight: responsive.getMaxRpxHeight(),
          platformClass: responsive.getPlatformClass(),
          screenClass: responsive.getScreenClass()
        });
      },
      
      // px转rpx
      pxToRpx(px) {
        return responsive.pxToRpx(px);
      },
      
      // rpx转px
      rpxToPx(rpx) {
        return responsive.rpxToPx(rpx);
      },
      
      // 获取自适应尺寸
      getAdaptiveSize(config) {
        return responsive.getAdaptiveSize(config);
      },
      
      // 创建响应式样式
      createResponsiveStyle(styleConfig) {
        return responsive.createResponsiveStyle(styleConfig);
      }
    }
  }
};

export default responsive;
